package com.fenbei.fx.card.common.enums;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.luastar.swift.base.utils.ObjUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 交易类型枚举
 */
public enum TransactionTypeEnum {
    PRE_AUTH(1,"预授权"),
    PRE_AUTH_RELEASE(4,"预授权撤销"),
    CONSUME_FAILED(5,"交易失败"),
    CONSUME(11,"消费"),
    REFUND(12,"退款"),

    WRONG_PAID(13,"错花还款");

    TransactionTypeEnum(Integer key,String value){
        this.key = key;
        this.value = value;
    }

    private final Integer key;

    private final String value;

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

    public static boolean isConsume(Integer key){
        return Objects.equals(CONSUME.getKey(), key);
    }
    public static boolean isRefund(Integer key){
        return Objects.equals(REFUND.getKey(), key);
    }

    public static String getPlusOrMinusValue(Integer key){
        if (Objects.equals(key, PRE_AUTH.key) || Objects.equals(key, CONSUME.getKey())){
            return "-";
        }
        return "";
    }

    public static BigDecimal getPlusOrMinusValue(Integer key, BigDecimal price){
        if (Objects.equals(key, PRE_AUTH.key) || Objects.equals(key, CONSUME.getKey())){
            return price.multiply(new BigDecimal(-1));
        }
        return price;
    }

    public static BigDecimal getPlusOrMinusValue4Saas(Integer key, BigDecimal price){
        return getPlusOrMinusValue(key,price).multiply(new BigDecimal(-1));
    }

    public static String getVerPlusOrMinusValue(Integer key){
        if (Objects.equals(key, PRE_AUTH.key) || Objects.equals(key, CONSUME.getKey())){
            return "";
        } else{
            return "-";
        }
    }
    public static BigDecimal getVerPlusOrMinusValue(Integer key, BigDecimal price){
        if (Objects.equals(key, PRE_AUTH.key) || Objects.equals(key, CONSUME.getKey())){
            return price;
        } else {
            return price.multiply(new BigDecimal(-1));
        }
    }

    private static final Map<Integer, TransactionTypeEnum> ENUM_MAP = Maps.newHashMap();

    static {
        for (TransactionTypeEnum item : values()) {
            ENUM_MAP.put(item.getKey(), item);
        }
    }

    public static TransactionTypeEnum getEnum(Integer key) {
        return ENUM_MAP.get(key);
    }

    public static List<Integer> getTradeEnumValues(){
        return  Lists.newArrayList(CONSUME.getKey(),REFUND.getKey(),WRONG_PAID.getKey());
    }
 }
